{"name": "bigo_backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/AK-shat-JAIN/big0_backend.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/AK-shat-JAIN/big0_backend/issues"}, "homepage": "https://github.com/AK-shat-JAIN/big0_backend#readme", "description": "", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^2.5.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "email-validation": "^0.1.2", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.5", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.16", "nodemon": "^3.1.9"}}