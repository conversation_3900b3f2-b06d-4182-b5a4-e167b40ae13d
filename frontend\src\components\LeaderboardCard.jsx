import { useState } from 'react';
import PropTypes from 'prop-types';
import { Code, Clock, HardDrive, Trophy, Medal, Award } from 'lucide-react';
import FullScreenModal from './FullScreenModal';

const LeaderboardCard = ({ solutions }) => {
  const [selectedSolution, setSelectedSolution] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = (solution) => {
    setSelectedSolution(solution);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  // Format language for display
  const getLanguageDisplay = (lang) => {
    const langMap = {
      'python3': 'Python',
      'javascript': 'JavaScript',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C'
    };
    return langMap[lang] || lang;
  };

  // Get medal icon based on rank
  const getMedalIcon = (index) => {
    switch (index) {
      case 0:
        return <Trophy size={16} className="text-yellow-400" />;
      case 1:
        return <Medal size={16} className="text-gray-300" />;
      case 2:
        return <Award size={16} className="text-amber-600" />;
      default:
        return null;
    }
  };

  // Get background color based on rank
  const getRowBackground = (index) => {
    switch (index) {
      case 0:
        return 'bg-yellow-500/10 border-l-4 border-yellow-500';
      case 1:
        return 'bg-gray-500/10 border-l-4 border-gray-300';
      case 2:
        return 'bg-amber-500/10 border-l-4 border-amber-600';
      default:
        return 'hover:bg-purple-500/5';
    }
  };

  return (
    <div className="leaderboard-card bg-gray-900/40 backdrop-blur-sm rounded-xl overflow-hidden border border-purple-500/20 shadow-xl h-76 mt-5 flex flex-col">
      <div className="py-3 px-4 bg-gradient-to-r from-purple-600 to-pink-600">
        <h3 className="text-xl font-bold text-white flex items-center gap-2">
          <Trophy size={20} />
          Community Leaderboard
        </h3>
      </div>

      <div className="overflow-hidden flex-1 flex flex-col">
        <table className="w-full text-left">
          <thead className="bg-gray-800/50 text-gray-300 text-sm">
            <tr>
              <th className="py-2 px-4 font-medium">#</th>
              <th className="py-2 px-4 font-medium">User</th>
              <th className="py-2 px-4 font-medium">Lang</th>
              <th className="py-2 px-4 font-medium">Runtime</th>
              <th className="py-2 px-4 font-medium">Memory</th>
              <th className="py-2 px-4 font-medium"></th>
            </tr>
          </thead>
          <tbody className="text-gray-300 divide-y divide-gray-800">
            {solutions.length === 0 ? (
              <tr>
                <td colSpan="6" className="py-6 text-center text-gray-400">
                  No solutions submitted yet. Be the first!
                </td>
              </tr>
            ) : (
              solutions.map((solution, index) => (
                <tr
                  key={solution.id}
                  className={`${getRowBackground(index)} transition-colors cursor-pointer hover:bg-purple-500/10`}
                  onClick={() => openModal(solution)}
                >
                  <td className="py-2 px-4 font-medium flex items-center">
                    {getMedalIcon(index)}
                    <span className="ml-1">{index + 1}</span>
                  </td>
                  <td className="py-2 px-4 font-medium">{solution.username}</td>
                  <td className="py-2 px-4">{getLanguageDisplay(solution.language)}</td>
                  <td className="py-2 px-4">
                    {solution.stats?.runtime || 'N/A'}
                  </td>
                  <td className="py-2 px-4">
                    {solution.stats?.memory || 'N/A'}
                  </td>
                  <td className="py-2 px-4 text-right">
                    <button className="text-purple-400 hover:text-purple-300 transition-colors">
                      <Code size={16} />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {selectedSolution && (
        <FullScreenModal
          isOpen={isModalOpen}
          onClose={closeModal}
          title={`${selectedSolution.username}'s Solution (${getLanguageDisplay(selectedSolution.language)})`}
        >
          <div className="space-y-8">
            {/* Stats Card */}
            <div className="bg-gray-800/50 p-6 rounded-xl border border-purple-500/20 shadow-xl">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center gap-4">
                  <div className="bg-purple-500/20 p-3 rounded-full">
                    <Clock size={24} className="text-purple-400" />
                  </div>
                  <div>
                    <div className="text-gray-400 text-sm">Runtime</div>
                    <div className="text-white text-lg font-medium">{selectedSolution.stats?.runtime || 'N/A'}</div>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="bg-purple-500/20 p-3 rounded-full">
                    <HardDrive size={24} className="text-purple-400" />
                  </div>
                  <div>
                    <div className="text-gray-400 text-sm">Memory</div>
                    <div className="text-white text-lg font-medium">{selectedSolution.stats?.memory || 'N/A'}</div>
                  </div>
                </div>
              </div>

              {selectedSolution.stats?.runtimePercentile && (
                <div className="mt-6">
                  <div className="flex justify-between text-sm text-gray-400 mb-2">
                    <span>Runtime Percentile</span>
                    <span>{selectedSolution.stats.runtimePercentile?.toFixed(2)}%</span>
                  </div>
                  <div className="h-2.5 w-full bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
                      style={{ width: `${selectedSolution.stats.runtimePercentile}%` }}
                    ></div>
                  </div>

                  {selectedSolution.stats?.memoryPercentile && (
                    <div className="mt-4">
                      <div className="flex justify-between text-sm text-gray-400 mb-2">
                        <span>Memory Percentile</span>
                        <span>{selectedSolution.stats.memoryPercentile?.toFixed(2)}%</span>
                      </div>
                      <div className="h-2.5 w-full bg-gray-700 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
                          style={{ width: `${selectedSolution.stats.memoryPercentile}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Approach Section */}
            {selectedSolution.approach && (
              <div className="bg-gray-800/50 p-6 rounded-xl border border-purple-500/20 shadow-xl">
                <div className="flex items-center gap-3 mb-6">
                  <Trophy size={24} className="text-purple-400" />
                  <h3 className="text-white text-xl font-semibold">Approach</h3>
                </div>
                <pre className="text-gray-300 text-base whitespace-pre-wrap bg-gray-900/70 p-6 rounded-lg">
                  {selectedSolution.approach}
                </pre>
              </div>
            )}

            {/* Solution Code Section */}
            <div className="bg-gray-800/50 p-6 rounded-xl border border-purple-500/20 shadow-xl">
              <div className="flex items-center gap-3 mb-6">
                <Code size={24} className="text-purple-400" />
                <h3 className="text-white text-xl font-semibold">Solution Code</h3>
              </div>
              <div className="overflow-auto bg-gray-900/70 p-6 rounded-lg" style={{ maxHeight: 'calc(100vh - 300px)' }}>
                <pre className="text-gray-300 text-base whitespace-pre-wrap">
                  <code>{selectedSolution.code}</code>
                </pre>
              </div>
            </div>
          </div>
        </FullScreenModal>
      )}
    </div>
  );
};

LeaderboardCard.propTypes = {
  solutions: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      username: PropTypes.string.isRequired,
      code: PropTypes.string.isRequired,
      language: PropTypes.string.isRequired,
      approach: PropTypes.string,
      stats: PropTypes.shape({
        runtime: PropTypes.string,
        memory: PropTypes.string,
        runtimePercentile: PropTypes.number,
        memoryPercentile: PropTypes.number
      })
    })
  ).isRequired
};

export default LeaderboardCard;
